import React, { useEffect, useRef, useState, useMemo } from 'react';
import { useLanguage } from '@/hooks/use-language';
import ScrollReveal from './ScrollReveal';
import Logo from './Logo';
import HeroOrbitingIcons from './HeroOrbitingIcons';
import { ChevronDown } from 'lucide-react'; // Added ChevronDown for scroll indicator

const HeroSection: React.FC = () => {
  const { t } = useLanguage();
  const scrollIndicatorRef = useRef<HTMLDivElement>(null);
  
  // Handle scroll action when indicator is clicked
  const handleScrollClick = () => {
    const nextSection = document.querySelector('#home + section');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Cleanup function for animations
  useEffect(() => {
    return () => {
      // Cleanup any animations if needed
    };
  }, []);

  return (
    <section id="home" className="min-h-[100vh] flex flex-col items-center justify-between py-8 sm:py-16 md:py-20 px-3 sm:px-6 md:px-8 relative overflow-hidden max-w-full">
      {/* Unified background layers for optimal readability and visual harmony */}
      <div className="absolute inset-0 bg-gradient-cosmic-dark"></div>
      <div className="absolute inset-0 bg-gradient-cosmic opacity-50"></div>
      <div className="absolute inset-0 bg-gradient-radial from-neon-purple/4 via-transparent to-neon-cyan/2"></div>
      <div className="absolute inset-0 bg-gradient-aurora opacity-1"></div>
      <div className="absolute inset-0 bg-black/40"></div>

      {/* Animated Orbital Icons System - Optimized for performance */}
      <HeroOrbitingIcons className="z-0 opacity-80" />


      {/* Simplified gradient orbs for cleaner appearance */}
      <div className="absolute top-20 right-1/4 w-32 h-32 sm:w-40 sm:h-40 md:w-64 md:h-64 bg-gradient-ocean opacity-15 rounded-full blur-3xl z-5"></div>
      <div className="absolute bottom-20 left-1/4 w-36 h-36 sm:w-48 sm:h-48 md:w-80 md:h-80 bg-gradient-sunset opacity-10 rounded-full blur-3xl z-5"></div>

      <div className="container max-w-5xl mx-auto text-center relative z-20 flex-1 flex flex-col items-center justify-center">
        {/* Centered content with enhanced z-index */}
        <div className="flex flex-col items-center animate-fade-in w-full">
          <ScrollReveal delay={300} className="animate-float w-[85%] sm:w-auto max-w-full">
            <div className="inline-block mb-5 sm:mb-6 px-5 sm:px-6 md:px-8 lg:px-10 py-3 sm:py-3 md:py-4 lg:py-5 rounded-full premium-glass transition-all duration-300">
              <Logo variant="hero" className="transform scale-110 sm:scale-100" />
            </div>
          </ScrollReveal>

          <ScrollReveal delay={500}>
            <h1 className="text-3xl sm:text-3xl md:text-5xl lg:text-6xl xl:text-7xl font-black section-title-ultra-bold mb-4 sm:mb-5 md:mb-8 leading-tight max-w-4xl px-3 sm:px-0">
            <span className="text-gradient-lightning-white drop-shadow-glow">We Build</span> <TypedText className="text-gradient-lightning-white drop-shadow-glow" />
          </h1>
          </ScrollReveal>

          <ScrollReveal delay={700}>
            <p className="text-base sm:text-base md:text-xl lg:text-2xl text-foreground/80 mb-6 sm:mb-6 md:mb-8 max-w-3xl mx-auto px-4 sm:px-2 leading-relaxed animate-fade-up">
              Premium web development and digital experiences for luxury brands
            </p>
          </ScrollReveal>

          <ScrollReveal delay={900}>
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 items-center justify-center mb-8 sm:mb-12">
              <a
                href="#gallery"
                className="premium-button text-black font-semibold px-8 py-4 rounded-xl shadow-lg transition-all duration-300 hover:scale-105 active:scale-95 min-h-[48px] min-w-[160px] flex items-center justify-center"
              >
                View Our Work
              </a>
              <a
                href="#contact"
                className="bg-gradient-neon text-background font-semibold px-8 py-4 rounded-xl shadow-lg transition-all duration-300 hover:scale-105 active:scale-95 hover:shadow-[0_8px_30px_rgba(0,229,229,0.5)] min-h-[48px] min-w-[160px] flex items-center justify-center"
              >
                Get Quote
              </a>
            </div>
          </ScrollReveal>


        </div>
      </div>

      {/* Scroll Down Animation repositioned to bottom of hero section */}
      <div className="w-full flex justify-center mb-6 sm:mb-8 md:mb-10 relative z-20">
        <ScrollReveal delay={900}>
          <div 
            ref={scrollIndicatorRef} 
            onClick={handleScrollClick} 
            className="cursor-pointer transition-all duration-300 animate-pulse-subtle hover:scale-110 active:scale-95 py-2 px-4 min-h-[48px] min-w-[48px] touch-manipulation"
          >
            <div className="flex flex-col items-center gap-2 sm:gap-2">
              <span className="text-premium-gold/80 text-sm sm:text-sm tracking-widest uppercase">Scroll Down</span>
              <div className="relative py-1">
                <div className="absolute animate-chevron-1">
                  <ChevronDown size={24} className="sm:size-6 text-premium-gold/80" />
                </div>
                <div className="absolute animate-chevron-2">
                  <ChevronDown size={24} className="sm:size-6 text-premium-gold/60" />
                </div>
                <div className="absolute animate-chevron-3">
                  <ChevronDown size={24} className="sm:size-6 text-premium-gold/40" />
                </div>
              </div>
            </div>
          </div>
        </ScrollReveal>
      </div>
    </section>
  );
};

// TypedText component for moderate typing animation effect  
interface TypedTextProps {
  className?: string;
}

const TypedText: React.FC<TypedTextProps> = ({ className }) => {
  // Array of phrases to cycle through - same content as original
  const phrases = useMemo(() => [
    "Digital Experiences",
    "Modern Web Solutions", 
    "Ideas Into Reality",
    "Development & Design",
    "Digital Growth",
    "Technology Solutions",
    "Premium Websites",
    "Your Online Presence"
  ], []);

  const [currentText, setCurrentText] = useState('');
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(true);
  const [showCursor, setShowCursor] = useState(true);
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Check for reduced motion preference
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      setPrefersReducedMotion(mediaQuery.matches);

      const handleChange = (e: MediaQueryListEvent) => setPrefersReducedMotion(e.matches);
      mediaQuery.addEventListener('change', handleChange);

      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, []);

  // Cursor blinking effect
  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 530); // Moderate cursor blink rate

    return () => clearInterval(cursorInterval);
  }, []);

  // Main typing animation effect with optimized speed for better UX
  useEffect(() => {
    if (prefersReducedMotion) {
      // For users who prefer reduced motion, just display the first phrase
      setCurrentText(phrases[0]);
      return;
    }

    const runTyping = () => {
      const targetPhrase = phrases[currentPhraseIndex];

      if (isTyping) {
        // Typing phase - Significantly faster for better UX
        if (currentText.length < targetPhrase.length) {
          setCurrentText(targetPhrase.slice(0, currentText.length + 1));
          timeoutRef.current = setTimeout(runTyping, 8); // 8ms per character (much faster)
        } else {
          // Finished typing, pause then start deleting
          timeoutRef.current = setTimeout(() => {
            setIsTyping(false);
            runTyping();
          }, 800); // 0.8s pause after typing (reduced for better flow)
        }
      } else {
        // Deleting phase - Optimized speed
        if (currentText.length > 0) {
          setCurrentText(currentText.slice(0, -1));
          timeoutRef.current = setTimeout(runTyping, 10); // 10ms per character deletion (faster)
        } else {
          // Finished deleting, move to next phrase
          setCurrentPhraseIndex((prev) => (prev + 1) % phrases.length);
          setIsTyping(true);
          timeoutRef.current = setTimeout(runTyping, 100); // 0.1s pause before next phrase (quicker transition)
        }
      }
    };

    timeoutRef.current = setTimeout(runTyping, 200); // 0.2s initial delay (faster start)

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [currentText, currentPhraseIndex, isTyping, phrases, prefersReducedMotion]);

  return (
    <span className={className} data-testid="typed-text">
      <span data-phrase-index={currentPhraseIndex} data-is-typing={isTyping}>
        {currentText}
      </span>
      <span
        className={`inline-block ${showCursor ? 'opacity-100' : 'opacity-0'} transition-opacity duration-200`}
        aria-hidden="true"
      >|</span>
      {/* Fallback for screen readers and reduced motion users */}
      <span className="sr-only">Econic Media - Digital Services Agency</span>
    </span>
  );
};

export default HeroSection;
